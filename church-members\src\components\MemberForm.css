/* Member Form Modal Styles - Mobile First */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
  -webkit-tap-highlight-color: transparent;
}

.close-button:hover {
  background-color: var(--surface-color);
  color: var(--text-primary);
}

.member-form {
  padding: var(--spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.required {
  color: var(--danger-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  min-height: var(--touch-target);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-family: inherit;
  background-color: white;
  transition: border-color 0.2s ease-in-out;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: var(--danger-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm) 0;
  -webkit-tap-highlight-color: transparent;
}

.checkbox-label input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  font-weight: 500;
  color: var(--text-primary);
  user-select: none;
}

.error-message {
  color: var(--danger-color);
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: var(--spacing-xs);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.form-actions button {
  min-width: 120px;
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .modal-overlay {
    padding: var(--spacing-xl);
  }
  
  .modal-header h2 {
    font-size: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr 1fr;
  }
  
  .form-group:last-child {
    align-self: end;
  }
  
  .checkbox-label {
    justify-content: center;
    padding: var(--spacing-md) 0;
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .modal-content {
    max-width: 700px;
  }
  
  .form-actions {
    justify-content: space-between;
  }
  
  .form-actions button {
    min-width: 140px;
  }
}

/* Animation for modal */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-content {
  animation: modalFadeIn 0.2s ease-out;
}

/* Focus management for accessibility */
.modal-overlay:focus-within .modal-content {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
