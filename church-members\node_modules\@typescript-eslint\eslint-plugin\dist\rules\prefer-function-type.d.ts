import type { TSESLint } from '@typescript-eslint/utils';
export declare const phrases: {
    readonly TSInterfaceDeclaration: "Interface";
    readonly TSTypeLiteral: "Type literal";
};
declare const _default: TSESLint.RuleModule<"functionTypeOverCallableType" | "unexpectedThisOnFunctionOnlyInterface", [], import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=prefer-function-type.d.ts.map