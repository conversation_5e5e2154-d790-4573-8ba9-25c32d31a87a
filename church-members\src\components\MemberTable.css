/* Member Table Styles - Mobile First */

.member-table-container {
  width: 100%;
}

.search-container {
  margin-bottom: var(--spacing-lg);
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

/* Mobile Card View */
.mobile-view {
  display: block;
}

.member-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.2s ease-in-out;
}

.member-card:hover {
  box-shadow: var(--shadow-md);
}

.member-card.critical {
  border-left: 4px solid var(--danger-color);
  background-color: #fef2f2;
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-sm);
}

.member-name h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.critical-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--danger-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.member-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.member-details {
  margin-bottom: var(--spacing-lg);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.detail-item .label {
  font-weight: 500;
  color: var(--text-primary);
}

.detail-item .yes {
  color: var(--success-color);
  font-weight: 500;
}

.detail-item .no {
  color: var(--text-muted);
}

.attendance-section h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.attendance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
}

.attendance-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.date-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Attendance Button Styles */
.attendance-btn {
  min-height: var(--touch-target);
  min-width: var(--touch-target);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}

.attendance-btn:hover {
  transform: scale(1.05);
}

.attendance-btn.present {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.attendance-btn.absent {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.attendance-btn.unmarked {
  background-color: var(--surface-color);
  border-color: var(--border-color);
  color: var(--text-muted);
}

.unmarked-dot {
  font-size: 1.5rem;
  line-height: 1;
}

/* Desktop Table View */
.desktop-view {
  display: none;
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .mobile-view {
    display: none;
  }
  
  .desktop-view {
    display: block;
  }
  
  .table-wrapper {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: white;
  }
  
  .member-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
  }
  
  .member-table th,
  .member-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }
  
  .member-table th {
    background-color: var(--surface-color);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  .member-table th button {
    background: none;
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0;
    font-size: inherit;
  }
  
  .member-table th button:hover {
    color: var(--primary-color);
  }
  
  .member-table tbody tr:hover {
    background-color: var(--surface-color);
  }
  
  .critical-row {
    background-color: #fef2f2;
  }
  
  .critical-row:hover {
    background-color: #fecaca;
  }
  
  .name-cell {
    min-width: 150px;
  }
  
  .name-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }
  
  .critical-icon {
    color: var(--danger-color);
    flex-shrink: 0;
  }
  
  .address-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .status.yes {
    color: var(--success-color);
    font-weight: 500;
  }
  
  .status.no {
    color: var(--text-muted);
  }
  
  .attendance-header {
    min-width: 60px;
    text-align: center;
  }
  
  .attendance-cell {
    text-align: center;
    padding: var(--spacing-xs);
  }
  
  .attendance-cell .attendance-btn {
    min-height: 32px;
    min-width: 32px;
  }
  
  .actions-cell {
    min-width: 100px;
  }
  
  .action-buttons {
    display: flex;
    gap: var(--spacing-xs);
  }
}

/* Large desktop screens */
@media (min-width: 1024px) {
  .member-table {
    font-size: 1rem;
  }
  
  .member-table th,
  .member-table td {
    padding: var(--spacing-md);
  }
  
  .attendance-cell .attendance-btn {
    min-height: var(--touch-target);
    min-width: var(--touch-target);
  }
}
