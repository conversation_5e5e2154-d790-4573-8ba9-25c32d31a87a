import React from 'react';
import { Grid, List, Table, BarChart3 } from 'lucide-react';
import './ViewSelector.css';

export type ViewType = 'table' | 'cards' | 'list' | 'stats';

interface ViewSelectorProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
}

const ViewSelector: React.FC<ViewSelectorProps> = ({
  currentView,
  onViewChange,
}) => {
  const views = [
    {
      id: 'table' as ViewType,
      name: 'Table View',
      description: 'Excel-like spreadsheet',
      icon: <Table size={20} />,
    },
    {
      id: 'cards' as ViewType,
      name: 'Card View',
      description: 'Modern card layout',
      icon: <Grid size={20} />,
    },
    {
      id: 'list' as ViewType,
      name: 'List View',
      description: 'Compact list format',
      icon: <List size={20} />,
    },
    {
      id: 'stats' as ViewType,
      name: 'Statistics',
      description: 'Analytics dashboard',
      icon: <BarChart3 size={20} />,
    },
  ];

  return (
    <div className="view-selector">
      <div className="view-selector-label">
        <span>View:</span>
      </div>
      <div className="view-options">
        {views.map((view) => (
          <button
            key={view.id}
            className={`view-option ${currentView === view.id ? 'active' : ''}`}
            onClick={() => onViewChange(view.id)}
            title={view.description}
          >
            <div className="view-icon">
              {view.icon}
            </div>
            <span className="view-name">{view.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ViewSelector;
