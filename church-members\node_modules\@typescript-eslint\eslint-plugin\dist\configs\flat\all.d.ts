import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Enables each the rules provided as a part of typescript-eslint. Note that many rules are not applicable in all codebases, or are meant to be configured.
 * @see {@link https://typescript-eslint.io/users/configs#all}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=all.d.ts.map