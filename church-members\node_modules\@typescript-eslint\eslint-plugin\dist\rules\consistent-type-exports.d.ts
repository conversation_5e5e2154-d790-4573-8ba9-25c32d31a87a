import type { TSESLint } from '@typescript-eslint/utils';
export type Options = [
    {
        fixMixedExportsWithInlineTypeSpecifier: boolean;
    }
];
export type MessageIds = 'multipleExportsAreTypes' | 'singleExportIsType' | 'typeOverValue';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=consistent-type-exports.d.ts.map