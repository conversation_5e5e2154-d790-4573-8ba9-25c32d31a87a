import React from 'react';
import type { MemberWithAttendance } from '../types';
import { formatPhoneNumber } from '../utils/dateUtils';
import { Edit, Trash2, Phone, MapPin, AlertTriangle, Heart } from 'lucide-react';
import './ListView.css';

interface ListViewProps {
  members: MemberWithAttendance[];
  onEditMember: (member: MemberWithAttendance) => void;
  onDeleteMember: (memberId: string) => void;
}

const ListView: React.FC<ListViewProps> = ({
  members,
  onEditMember,
  onDeleteMember,
}) => {
  if (members.length === 0) {
    return (
      <div className="list-empty-state">
        <div className="empty-content">
          <h3>No Members Yet</h3>
          <p>Start building your church community by adding your first member!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="list-view-container">
      <div className="list-header">
        <span className="member-count">{members.length} member{members.length !== 1 ? 's' : ''}</span>
      </div>
      
      <div className="list-items">
        {members.map((member, index) => (
          <div key={member.id} className={`list-item ${member.isCritical ? 'critical' : ''}`}>
            <div className="list-item-number">
              {index + 1}
            </div>
            
            <div className="list-item-content">
              <div className="member-main-info">
                <div className="member-name">
                  <span className="name-text">{member.firstName} {member.lastName}</span>
                  {member.bornAgainStatus && (
                    <Heart size={16} className="born-again-icon" />
                  )}
                  {member.isCritical && (
                    <div className="critical-badge">
                      <AlertTriangle size={14} />
                      <span>{member.consecutiveAbsences} absences</span>
                    </div>
                  )}
                </div>
                
                <div className="member-details">
                  <div className="detail-item">
                    <Phone size={14} />
                    <span>{formatPhoneNumber(member.phoneNumber)}</span>
                  </div>
                  <div className="detail-item">
                    <MapPin size={14} />
                    <span>{member.buildingAddress}</span>
                  </div>
                  <div className="detail-item group-info">
                    <span className="group-label">Group:</span>
                    <span className="group-name">{member.congregationGroup}</span>
                  </div>
                </div>
              </div>
              
              <div className="list-item-actions">
                <button
                  className="list-action-btn edit"
                  onClick={() => onEditMember(member)}
                  title="Edit member"
                >
                  <Edit size={16} />
                </button>
                <button
                  className="list-action-btn delete"
                  onClick={() => onDeleteMember(member.id)}
                  title="Delete member"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ListView;
