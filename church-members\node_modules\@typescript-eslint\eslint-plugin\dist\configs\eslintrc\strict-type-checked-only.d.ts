declare const _default: {
    extends: string[];
    rules: {
        '@typescript-eslint/await-thenable': "error";
        '@typescript-eslint/no-array-delete': "error";
        '@typescript-eslint/no-base-to-string': "error";
        '@typescript-eslint/no-confusing-void-expression': "error";
        '@typescript-eslint/no-deprecated': "error";
        '@typescript-eslint/no-duplicate-type-constituents': "error";
        '@typescript-eslint/no-floating-promises': "error";
        '@typescript-eslint/no-for-in-array': "error";
        'no-implied-eval': "off";
        '@typescript-eslint/no-implied-eval': "error";
        '@typescript-eslint/no-meaningless-void-operator': "error";
        '@typescript-eslint/no-misused-promises': "error";
        '@typescript-eslint/no-misused-spread': "error";
        '@typescript-eslint/no-mixed-enums': "error";
        '@typescript-eslint/no-redundant-type-constituents': "error";
        '@typescript-eslint/no-unnecessary-boolean-literal-compare': "error";
        '@typescript-eslint/no-unnecessary-condition': "error";
        '@typescript-eslint/no-unnecessary-template-expression': "error";
        '@typescript-eslint/no-unnecessary-type-arguments': "error";
        '@typescript-eslint/no-unnecessary-type-assertion': "error";
        '@typescript-eslint/no-unnecessary-type-parameters': "error";
        '@typescript-eslint/no-unsafe-argument': "error";
        '@typescript-eslint/no-unsafe-assignment': "error";
        '@typescript-eslint/no-unsafe-call': "error";
        '@typescript-eslint/no-unsafe-enum-comparison': "error";
        '@typescript-eslint/no-unsafe-member-access': "error";
        '@typescript-eslint/no-unsafe-return': "error";
        '@typescript-eslint/no-unsafe-unary-minus': "error";
        'no-throw-literal': "off";
        '@typescript-eslint/only-throw-error': "error";
        'prefer-promise-reject-errors': "off";
        '@typescript-eslint/prefer-promise-reject-errors': "error";
        '@typescript-eslint/prefer-reduce-type-parameter': "error";
        '@typescript-eslint/prefer-return-this-type': "error";
        '@typescript-eslint/related-getter-setter-pairs': "error";
        'require-await': "off";
        '@typescript-eslint/require-await': "error";
        '@typescript-eslint/restrict-plus-operands': ["error", {
            allowAny: boolean;
            allowBoolean: boolean;
            allowNullish: boolean;
            allowNumberAndString: boolean;
            allowRegExp: boolean;
        }];
        '@typescript-eslint/restrict-template-expressions': ["error", {
            allowAny: boolean;
            allowBoolean: boolean;
            allowNever: boolean;
            allowNullish: boolean;
            allowNumber: boolean;
            allowRegExp: boolean;
        }];
        'no-return-await': "off";
        '@typescript-eslint/return-await': ["error", string];
        '@typescript-eslint/unbound-method': "error";
        '@typescript-eslint/use-unknown-in-catch-callback-variable': "error";
    };
};
export = _default;
//# sourceMappingURL=strict-type-checked-only.d.ts.map