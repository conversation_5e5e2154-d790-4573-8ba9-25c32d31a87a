/* Tab Management Styles */

.tab-management-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
  animation: fadeIn 0.3s ease-out;
}

.tab-management-content {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideInUp 0.3s ease-out;
}

.tab-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  background: var(--gradient-primary);
  color: white;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.tab-management-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-button {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.tab-management-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.system-tabs-section,
.user-tabs-section {
  background: var(--background-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-light);
}

.system-tabs-section h3,
.user-tabs-section h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.section-description {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.add-tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
}

.tabs-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.tab-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
  background: var(--surface-color);
  transition: all var(--transition-fast);
}

.tab-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.tab-item.system {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-color: var(--border-light);
}

.tab-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tab-name-with-icon {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.tab-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.tab-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.tab-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tab-badge.system {
  background: var(--border-light);
  color: var(--text-muted);
}

.tab-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  min-width: 50px;
  height: 32px;
  border-radius: var(--radius-md);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0 var(--spacing-sm);
}

.action-btn.edit {
  background: var(--gradient-primary);
  color: white;
}

.action-btn.edit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.action-btn.delete {
  background: var(--gradient-danger);
  color: white;
}

.action-btn.delete:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.tab-form {
  background: var(--surface-color);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-group input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background: var(--background-color);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
  font-style: italic;
}

.tab-management-footer {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--background-color);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .tab-management-overlay {
    padding: var(--spacing-md);
  }
  
  .tab-management-content {
    max-height: 95vh;
  }
  
  .tab-management-header {
    padding: var(--spacing-lg);
  }
  
  .header-title-section {
    gap: var(--spacing-sm);
  }
  
  .header-icon {
    width: 40px;
    height: 40px;
  }
  
  .tab-management-header h2 {
    font-size: 1.25rem;
  }
  
  .tab-management-body {
    padding: var(--spacing-lg);
  }
  
  .system-tabs-section,
  .user-tabs-section {
    padding: var(--spacing-lg);
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .tab-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .tab-actions {
    justify-content: flex-end;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .tab-management-footer {
    flex-direction: column-reverse;
  }
}

@media (max-width: 480px) {
  .tab-management-overlay {
    padding: var(--spacing-sm);
  }
  
  .tab-management-header {
    padding: var(--spacing-md);
  }
  
  .tab-management-body {
    padding: var(--spacing-md);
  }
  
  .system-tabs-section,
  .user-tabs-section {
    padding: var(--spacing-md);
  }
  
  .tab-form {
    padding: var(--spacing-md);
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
