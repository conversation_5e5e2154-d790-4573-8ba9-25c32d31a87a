/* Excel-like Table View Styles */

.excel-table-container {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.excel-table-wrapper {
  overflow: auto;
  max-height: 70vh;
  position: relative;
}

.excel-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.875rem;
  background: var(--surface-color);
}

/* Header Styles */
.excel-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--gradient-primary);
  color: white;
}

.excel-th {
  padding: var(--spacing-md) var(--spacing-sm);
  text-align: left;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  position: relative;
  user-select: none;
}

.excel-th:last-child {
  border-right: none;
}

.excel-th.sortable {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.excel-th.sortable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xs);
}

.sort-icon {
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

/* Column Widths */
.row-number {
  width: 50px;
  text-align: center;
}

.name-cell {
  min-width: 120px;
}

.phone-cell {
  min-width: 140px;
}

.address-col {
  min-width: 200px;
  max-width: 250px;
}

.group-col {
  min-width: 120px;
}

.attendance-col {
  width: 80px;
  text-align: center;
}

.actions-col {
  width: 100px;
  text-align: center;
}

.attendance-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.date-text {
  font-size: 0.75rem;
  line-height: 1;
}

/* Body Styles */
.excel-body {
  background: var(--surface-color);
}

.excel-row {
  transition: all var(--transition-fast);
  border-bottom: 1px solid var(--border-light);
}

.excel-row:hover {
  background: var(--primary-light);
}

.excel-row.critical-row {
  background: linear-gradient(90deg, #fef2f2 0%, #ffffff 100%);
  border-left: 4px solid var(--danger-color);
}

.excel-row.critical-row:hover {
  background: linear-gradient(90deg, #fecaca 0%, var(--primary-light) 100%);
}

.excel-cell {
  padding: var(--spacing-sm);
  border-right: 1px solid var(--border-light);
  vertical-align: middle;
  position: relative;
}

.excel-cell:last-child {
  border-right: none;
}

.excel-cell.row-number {
  text-align: center;
  font-weight: 600;
  color: var(--text-muted);
  background: var(--background-color);
  border-right: 2px solid var(--border-color);
}

.excel-cell.address-cell {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-cell.phone-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.yes {
  background: var(--success-light);
  color: var(--success-color);
}

.status-indicator.no {
  background: var(--border-light);
  color: var(--text-muted);
}

/* Attendance Cells */
.attendance-cell {
  text-align: center;
  padding: var(--spacing-xs);
}

.excel-attendance-btn {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  background: var(--surface-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.excel-attendance-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.excel-attendance-btn.present {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.excel-attendance-btn.absent {
  background: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.excel-attendance-btn.unmarked {
  background: var(--border-light);
  border-color: var(--border-color);
}

.unmarked-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-muted);
  opacity: 0.5;
}

/* Alert Indicators */
.alerts-cell {
  text-align: center;
}

.alert-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--danger-light);
  color: var(--danger-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  animation: pulse 2s infinite;
}

/* Action Buttons */
.actions-cell {
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
}

.excel-action-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.excel-action-btn.edit {
  background: var(--primary-color);
  color: white;
}

.excel-action-btn.edit:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.excel-action-btn.delete {
  background: var(--danger-color);
  color: white;
}

.excel-action-btn.delete:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Empty State */
.excel-empty-state {
  padding: var(--spacing-3xl);
  text-align: center;
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-color);
}

.empty-content h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.empty-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .excel-table-wrapper {
    max-height: 60vh;
  }
  
  .excel-th,
  .excel-cell {
    padding: var(--spacing-xs);
  }
  
  .excel-th {
    font-size: 0.7rem;
  }
  
  .excel-table {
    font-size: 0.8rem;
  }
  
  .address-col {
    max-width: 150px;
  }
  
  .excel-attendance-btn {
    width: 28px;
    height: 28px;
  }
  
  .excel-action-btn {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .excel-table-wrapper {
    max-height: 50vh;
  }
  
  .excel-th,
  .excel-cell {
    padding: 2px;
  }
  
  .excel-table {
    font-size: 0.75rem;
  }
  
  .address-col {
    max-width: 100px;
  }
  
  .excel-attendance-btn {
    width: 24px;
    height: 24px;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
