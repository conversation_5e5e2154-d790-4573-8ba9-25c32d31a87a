import type { TSESTree } from '@typescript-eslint/types';
import { DefinitionBase } from './DefinitionBase';
import { DefinitionType } from './DefinitionType';
export declare class TSEnumMemberDefinition extends DefinitionBase<DefinitionType.TSEnumMember, TSESTree.TSEnumMember, null, TSESTree.Identifier | TSESTree.StringLiteral> {
    readonly isTypeDefinition = true;
    readonly isVariableDefinition = true;
    constructor(name: TSESTree.Identifier | TSESTree.StringLiteral, node: TSEnumMemberDefinition['node']);
}
//# sourceMappingURL=TSEnumMemberDefinition.d.ts.map