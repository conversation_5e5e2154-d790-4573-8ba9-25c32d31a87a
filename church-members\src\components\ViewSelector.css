/* View Selector Styles */

.view-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--surface-color);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.view-selector-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  white-space: nowrap;
}

.view-options {
  display: flex;
  gap: var(--spacing-xs);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--background-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  min-height: 40px;
}

.view-option:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.view-option.active {
  background: var(--gradient-primary);
  border-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.view-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.view-name {
  font-weight: 600;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .view-selector {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .view-selector-label {
    text-align: center;
  }
  
  .view-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .view-option {
    justify-content: center;
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .view-options {
    grid-template-columns: 1fr;
  }
  
  .view-option {
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
  }
  
  .view-name {
    font-size: 0.8rem;
  }
}
