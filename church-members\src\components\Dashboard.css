/* Dashboard Styles - Mobile First */

.dashboard {
  padding: var(--spacing-lg) 0;
}

.dashboard-header {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.dashboard-header h1 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.dashboard-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.section {
  margin-bottom: var(--spacing-2xl);
}

.section h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 1.25rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
}

.stat-card.primary {
  border-left-color: var(--primary-color);
}

.stat-card.success {
  border-left-color: var(--success-color);
}

.stat-card.warning {
  border-left-color: var(--warning-color);
}

.stat-card.danger {
  border-left-color: var(--danger-color);
}

.stat-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
}

.stat-card.primary .stat-icon {
  background-color: rgb(37 99 235 / 0.1);
  color: var(--primary-color);
}

.stat-card.success .stat-icon {
  background-color: rgb(16 185 129 / 0.1);
  color: var(--success-color);
}

.stat-card.warning .stat-icon {
  background-color: rgb(245 158 11 / 0.1);
  color: var(--warning-color);
}

.stat-card.danger .stat-icon {
  background-color: rgb(239 68 68 / 0.1);
  color: var(--danger-color);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-subtitle {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.stat-trend {
  font-size: 0.75rem;
  color: var(--success-color);
  font-weight: 500;
}

/* Congregation Grid */
.congregation-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.congregation-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.congregation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.congregation-header h3 {
  margin: 0;
  font-size: 1.125rem;
  color: var(--text-primary);
}

.congregation-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.congregation-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.congregation-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.congregation-stat .stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.congregation-stat .stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.congregation-stat .stat-value.critical {
  color: var(--danger-color);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--surface-color);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--success-color);
  transition: width 0.3s ease-in-out;
}

/* Critical Members List */
.critical-members-list {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.critical-member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-md);
}

.critical-member-item:last-child {
  border-bottom: none;
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.member-details {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.absence-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--danger-color);
  font-size: 0.875rem;
  font-weight: 500;
  flex-shrink: 0;
}

.more-critical {
  padding: var(--spacing-md);
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  background-color: var(--surface-color);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: var(--touch-target);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: white;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
}

.action-button:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.action-button.primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-button.primary:hover {
  background-color: var(--primary-hover);
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .dashboard-header {
    text-align: left;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .congregation-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .congregation-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .critical-member-item {
    padding: var(--spacing-lg);
  }
}
