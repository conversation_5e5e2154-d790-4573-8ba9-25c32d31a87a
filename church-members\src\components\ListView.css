/* List View Styles */

.list-view-container {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.list-header {
  padding: var(--spacing-lg);
  background: var(--gradient-primary);
  color: white;
  border-bottom: 1px solid var(--border-color);
}

.member-count {
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.list-items {
  max-height: 70vh;
  overflow-y: auto;
}

.list-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-fast);
  background: var(--surface-color);
}

.list-item:hover {
  background: var(--primary-light);
  transform: translateX(4px);
}

.list-item.critical {
  background: linear-gradient(90deg, #fef2f2 0%, #ffffff 100%);
  border-left: 4px solid var(--danger-color);
}

.list-item.critical:hover {
  background: linear-gradient(90deg, #fecaca 0%, var(--primary-light) 100%);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-number {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.list-item-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.member-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.member-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.name-text {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
}

.born-again-icon {
  color: var(--success-color);
  flex-shrink: 0;
}

.critical-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--gradient-danger);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.detail-item svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.group-info {
  margin-top: var(--spacing-xs);
}

.group-label {
  font-weight: 600;
  color: var(--text-primary);
}

.group-name {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.list-item-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.list-action-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.list-action-btn.edit {
  background: var(--gradient-primary);
  color: white;
}

.list-action-btn.edit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.list-action-btn.delete {
  background: var(--gradient-danger);
  color: white;
}

.list-action-btn.delete:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.list-empty-state {
  padding: var(--spacing-3xl);
  text-align: center;
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-color);
}

.empty-content h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.empty-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .list-item {
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .list-item-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .list-item-actions {
    justify-content: flex-end;
  }
  
  .member-details {
    gap: var(--spacing-sm);
  }
  
  .detail-item {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .list-item {
    padding: var(--spacing-sm);
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .list-item-number {
    align-self: flex-start;
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
  
  .list-item-content {
    gap: var(--spacing-sm);
  }
  
  .name-text {
    font-size: 1rem;
  }
  
  .list-action-btn {
    width: 36px;
    height: 36px;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
