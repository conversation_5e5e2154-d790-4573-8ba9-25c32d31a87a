import React from 'react';
import type { MemberWithAttendance, AttendanceRecord, Congregation } from '../types';
import { getCurrentMonthAttendancePercentage, getCriticalMembers } from '../utils/attendanceUtils';
import { Users, AlertTriangle, TrendingUp, Calendar } from 'lucide-react';
import './Dashboard.css';

interface DashboardProps {
  members: MemberWithAttendance[];
  attendanceRecords: AttendanceRecord[];
  congregations: Congregation[];
}

const Dashboard: React.FC<DashboardProps> = ({
  members,
  attendanceRecords,
  congregations,
}) => {
  const totalMembers = members.length;
  const criticalMembers = getCriticalMembers(members);
  const currentMonthAttendance = getCurrentMonthAttendancePercentage(members, attendanceRecords);

  // Calculate congregation statistics
  const congregationStats = congregations.map(congregation => {
    const congregationMembers = members.filter(m => m.congregationGroup === congregation.id);
    const congregationCritical = getCriticalMembers(congregationMembers);
    const congregationAttendance = getCurrentMonthAttendancePercentage(congregationMembers, attendanceRecords);

    return {
      id: congregation.id,
      name: congregation.name,
      totalMembers: congregationMembers.length,
      criticalMembers: congregationCritical.length,
      attendancePercentage: congregationAttendance,
    };
  });

  // Recent activity (last 7 days)
  const recentDate = new Date();
  recentDate.setDate(recentDate.getDate() - 7);
  const recentAttendance = attendanceRecords.filter(record => 
    new Date(record.date) >= recentDate
  );

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ReactNode;
    color: 'primary' | 'success' | 'warning' | 'danger';
    trend?: string;
  }> = ({ title, value, subtitle, icon, color, trend }) => (
    <div className={`stat-card ${color}`}>
      <div className="stat-icon">
        {icon}
      </div>
      <div className="stat-content">
        <div className="stat-value">{value}</div>
        <div className="stat-title">{title}</div>
        {subtitle && <div className="stat-subtitle">{subtitle}</div>}
        {trend && <div className="stat-trend">{trend}</div>}
      </div>
    </div>
  );

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <p className="dashboard-subtitle">
          Overview of church membership and attendance
        </p>
      </div>

      {/* Key Metrics */}
      <div className="stats-grid">
        <StatCard
          title="Total Members"
          value={totalMembers}
          icon={<Users size={24} />}
          color="primary"
          subtitle="Active members"
        />
        
        <StatCard
          title="Critical Members"
          value={criticalMembers.length}
          icon={<AlertTriangle size={24} />}
          color="danger"
          subtitle="Need contact"
        />
        
        <StatCard
          title="Monthly Attendance"
          value={`${Math.round(currentMonthAttendance)}%`}
          icon={<TrendingUp size={24} />}
          color="success"
          subtitle="Current month"
        />
        
        <StatCard
          title="Recent Activity"
          value={recentAttendance.length}
          icon={<Calendar size={24} />}
          color="primary"
          subtitle="Last 7 days"
        />
      </div>

      {/* Congregation Breakdown */}
      <div className="section">
        <h2>Congregation Overview</h2>
        <div className="congregation-grid">
          {congregationStats.map((congregation) => (
            <div key={congregation.id} className="congregation-card">
              <div className="congregation-header">
                <h3>{congregation.name}</h3>
                <div className="congregation-count">
                  {congregation.totalMembers} members
                </div>
              </div>
              
              <div className="congregation-stats">
                <div className="congregation-stat">
                  <span className="stat-label">Attendance</span>
                  <span className="stat-value">
                    {Math.round(congregation.attendancePercentage)}%
                  </span>
                </div>
                
                <div className="congregation-stat">
                  <span className="stat-label">Critical</span>
                  <span className={`stat-value ${congregation.criticalMembers > 0 ? 'critical' : ''}`}>
                    {congregation.criticalMembers}
                  </span>
                </div>
              </div>
              
              {/* Progress bar for attendance */}
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${Math.min(congregation.attendancePercentage, 100)}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Critical Members Alert */}
      {criticalMembers.length > 0 && (
        <div className="section">
          <h2>Members Requiring Attention</h2>
          <div className="critical-members-list">
            {criticalMembers.slice(0, 5).map((member) => (
              <div key={member.id} className="critical-member-item">
                <div className="member-info">
                  <div className="member-name">
                    {member.firstName} {member.lastName}
                  </div>
                  <div className="member-details">
                    {member.congregationGroup} • {member.phoneNumber}
                  </div>
                </div>
                <div className="absence-info">
                  <AlertTriangle size={16} />
                  <span>{member.consecutiveAbsences} consecutive absences</span>
                </div>
              </div>
            ))}
            {criticalMembers.length > 5 && (
              <div className="more-critical">
                +{criticalMembers.length - 5} more members need attention
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="section">
        <h2>Quick Actions</h2>
        <div className="quick-actions">
          <button className="action-button primary">
            <Users size={20} />
            <span>Add New Member</span>
          </button>
          
          <button className="action-button secondary">
            <Calendar size={20} />
            <span>Mark Attendance</span>
          </button>
          
          <button className="action-button secondary">
            <TrendingUp size={20} />
            <span>View Reports</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
