import React from 'react';
import type { MemberWithAttendance, AttendanceRecord, Congregation } from '../types';
import { getCurrentMonthAttendancePercentage, getCriticalMembers } from '../utils/attendanceUtils';
import { Users, AlertTriangle, TrendingUp, Heart, Phone } from 'lucide-react';
import './StatsView.css';

interface StatsViewProps {
  members: MemberWithAttendance[];
  attendanceRecords: AttendanceRecord[];
  congregations: Congregation[];
}

const StatsView: React.FC<StatsViewProps> = ({
  members,
  attendanceRecords,
  congregations,
}) => {
  const totalMembers = members.length;
  const criticalMembers = getCriticalMembers(members);
  const bornAgainMembers = members.filter(m => m.bornAgainStatus);
  const currentMonthAttendance = getCurrentMonthAttendancePercentage(members, attendanceRecords);

  // Calculate congregation statistics
  const congregationStats = congregations
    .filter(c => !['overview', 'critical', 'new-believers'].includes(c.id))
    .map(congregation => {
      const congregationMembers = members.filter(m => m.congregationGroup === congregation.id);
      const congregationCritical = getCriticalMembers(congregationMembers);
      const congregationAttendance = getCurrentMonthAttendancePercentage(congregationMembers, attendanceRecords);

      return {
        id: congregation.id,
        name: congregation.name,
        totalMembers: congregationMembers.length,
        criticalMembers: congregationCritical.length,
        attendancePercentage: congregationAttendance,
        bornAgainCount: congregationMembers.filter(m => m.bornAgainStatus).length,
      };
    });

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ReactNode;
    color: 'primary' | 'success' | 'warning' | 'danger';
    percentage?: number;
  }> = ({ title, value, subtitle, icon, color, percentage }) => (
    <div className={`stat-card ${color}`}>
      <div className="stat-icon">
        {icon}
      </div>
      <div className="stat-content">
        <div className="stat-value">{value}</div>
        <div className="stat-title">{title}</div>
        {subtitle && <div className="stat-subtitle">{subtitle}</div>}
        {percentage !== undefined && (
          <div className="stat-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${Math.min(percentage, 100)}%` }}
              />
            </div>
            <span className="progress-text">{Math.round(percentage)}%</span>
          </div>
        )}
      </div>
    </div>
  );

  if (totalMembers === 0) {
    return (
      <div className="stats-empty-state">
        <div className="empty-content">
          <Users size={64} className="empty-icon" />
          <h3>No Data to Display</h3>
          <p>Add members to see statistics and analytics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="stats-view-container">
      {/* Overview Statistics */}
      <div className="stats-section">
        <h2 className="section-title">Church Overview</h2>
        <div className="stats-grid">
          <StatCard
            title="Total Members"
            value={totalMembers}
            icon={<Users size={24} />}
            color="primary"
            subtitle="Active members"
          />
          
          <StatCard
            title="Born Again"
            value={bornAgainMembers.length}
            icon={<Heart size={24} />}
            color="success"
            subtitle={`${totalMembers > 0 ? Math.round((bornAgainMembers.length / totalMembers) * 100) : 0}% of members`}
            percentage={totalMembers > 0 ? (bornAgainMembers.length / totalMembers) * 100 : 0}
          />
          
          <StatCard
            title="Critical Members"
            value={criticalMembers.length}
            icon={<AlertTriangle size={24} />}
            color="danger"
            subtitle="Need attention"
          />
          
          <StatCard
            title="Monthly Attendance"
            value={`${Math.round(currentMonthAttendance)}%`}
            icon={<TrendingUp size={24} />}
            color="primary"
            subtitle="Current month"
            percentage={currentMonthAttendance}
          />
        </div>
      </div>

      {/* Congregation Breakdown */}
      {congregationStats.length > 0 && (
        <div className="stats-section">
          <h2 className="section-title">Congregation Breakdown</h2>
          <div className="congregation-stats-grid">
            {congregationStats.map((congregation) => (
              <div key={congregation.id} className="congregation-stat-card">
                <div className="congregation-header">
                  <h3>{congregation.name}</h3>
                  <div className="congregation-count">
                    {congregation.totalMembers} members
                  </div>
                </div>
                
                <div className="congregation-metrics">
                  <div className="metric">
                    <div className="metric-icon">
                      <TrendingUp size={16} />
                    </div>
                    <div className="metric-content">
                      <span className="metric-label">Attendance</span>
                      <span className="metric-value">
                        {Math.round(congregation.attendancePercentage)}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="metric">
                    <div className="metric-icon">
                      <Heart size={16} />
                    </div>
                    <div className="metric-content">
                      <span className="metric-label">Born Again</span>
                      <span className="metric-value">
                        {congregation.bornAgainCount}
                      </span>
                    </div>
                  </div>
                  
                  <div className="metric">
                    <div className="metric-icon">
                      <AlertTriangle size={16} />
                    </div>
                    <div className="metric-content">
                      <span className="metric-label">Critical</span>
                      <span className={`metric-value ${congregation.criticalMembers > 0 ? 'critical' : ''}`}>
                        {congregation.criticalMembers}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="congregation-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${Math.min(congregation.attendancePercentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Critical Members Alert */}
      {criticalMembers.length > 0 && (
        <div className="stats-section">
          <h2 className="section-title">Members Requiring Attention</h2>
          <div className="critical-members-stats">
            {criticalMembers.slice(0, 5).map((member) => (
              <div key={member.id} className="critical-member-stat">
                <div className="member-info">
                  <div className="member-name">
                    {member.firstName} {member.lastName}
                  </div>
                  <div className="member-contact">
                    <Phone size={14} />
                    <span>{member.phoneNumber}</span>
                  </div>
                </div>
                <div className="absence-stat">
                  <AlertTriangle size={16} />
                  <span>{member.consecutiveAbsences} consecutive absences</span>
                </div>
              </div>
            ))}
            {criticalMembers.length > 5 && (
              <div className="more-critical-stat">
                +{criticalMembers.length - 5} more members need attention
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatsView;
