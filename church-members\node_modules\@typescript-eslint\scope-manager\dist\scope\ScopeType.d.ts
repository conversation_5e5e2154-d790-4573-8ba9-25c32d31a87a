export declare enum ScopeType {
    block = "block",
    catch = "catch",
    class = "class",
    classFieldInitializer = "class-field-initializer",
    classStaticBlock = "class-static-block",
    conditionalType = "conditionalType",
    for = "for",
    function = "function",
    functionExpressionName = "function-expression-name",
    functionType = "functionType",
    global = "global",
    mappedType = "mappedType",
    module = "module",
    switch = "switch",
    tsEnum = "tsEnum",
    tsModule = "tsModule",
    type = "type",
    with = "with"
}
//# sourceMappingURL=ScopeType.d.ts.map