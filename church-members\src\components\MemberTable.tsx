import React, { useState } from 'react';
import type { MemberWithAttendance, AttendanceRecord } from '../types';
import { formatSundayColumn, formatDateForStorage, formatPhoneNumber } from '../utils/dateUtils';
import { getAttendanceStatus } from '../utils/attendanceUtils';
import { Edit, Trash2, Phone, MapPin, AlertTriangle, Check, X } from 'lucide-react';
import './MemberTable.css';

interface MemberTableProps {
  members: MemberWithAttendance[];
  attendanceRecords: AttendanceRecord[];
  sundays: Date[];
  onEditMember: (member: MemberWithAttendance) => void;
  onDeleteMember: (memberId: string) => void;
  onAttendanceToggle: (memberId: string, date: string, currentStatus: 'present' | 'absent' | null) => void;
}

const MemberTable: React.FC<MemberTableProps> = ({
  members,
  attendanceRecords,
  sundays,
  onEditMember,
  onDeleteMember,
  onAttendanceToggle,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof MemberWithAttendance>('lastName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter members based on search term
  const filteredMembers = members.filter(member =>
    `${member.firstName} ${member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.phoneNumber.includes(searchTerm) ||
    member.buildingAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.congregationGroup.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort members
  const sortedMembers = [...filteredMembers].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortDirection === 'asc' ? comparison : -comparison;
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return 0;
  });

  const handleSort = (field: keyof MemberWithAttendance) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const AttendanceButton: React.FC<{
    memberId: string;
    date: string;
    status: 'present' | 'absent' | null;
  }> = ({ memberId, date, status }) => (
    <button
      className={`attendance-btn ${status || 'unmarked'}`}
      onClick={() => onAttendanceToggle(memberId, date, status)}
      title={status === 'present' ? 'Present - Click to mark absent' : 
             status === 'absent' ? 'Absent - Click to mark present' : 
             'Not marked - Click to mark present'}
    >
      {status === 'present' ? <Check size={16} /> : 
       status === 'absent' ? <X size={16} /> : 
       <span className="unmarked-dot">•</span>}
    </button>
  );

  if (members.length === 0) {
    return (
      <div className="empty-state">
        <p>No members found. Add your first member to get started!</p>
      </div>
    );
  }

  return (
    <div className="member-table-container">
      {/* Search Bar */}
      <div className="search-container">
        <input
          type="text"
          placeholder="Search members..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
        />
      </div>

      {/* Mobile Card View */}
      <div className="mobile-view">
        {sortedMembers.map((member) => (
          <div key={member.id} className={`member-card ${member.isCritical ? 'critical' : ''}`}>
            <div className="member-header">
              <div className="member-name">
                <h3>{member.firstName} {member.lastName}</h3>
                {member.isCritical && (
                  <div className="critical-badge">
                    <AlertTriangle size={16} />
                    <span>Contact needed - {member.consecutiveAbsences} consecutive absences</span>
                  </div>
                )}
              </div>
              <div className="member-actions">
                <button
                  className="btn-sm btn-secondary"
                  onClick={() => onEditMember(member)}
                  title="Edit member"
                >
                  <Edit size={16} />
                </button>
                <button
                  className="btn-sm btn-danger"
                  onClick={() => onDeleteMember(member.id)}
                  title="Delete member"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            <div className="member-details">
              <div className="detail-item">
                <Phone size={14} />
                <span>{formatPhoneNumber(member.phoneNumber)}</span>
              </div>
              <div className="detail-item">
                <MapPin size={14} />
                <span>{member.buildingAddress}</span>
              </div>
              <div className="detail-item">
                <span className="label">Born Again:</span>
                <span className={member.bornAgainStatus ? 'yes' : 'no'}>
                  {member.bornAgainStatus ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="detail-item">
                <span className="label">Group:</span>
                <span>{member.congregationGroup}</span>
              </div>
            </div>

            <div className="attendance-section">
              <h4>Attendance</h4>
              <div className="attendance-grid">
                {sundays.map((sunday) => {
                  const dateStr = formatDateForStorage(sunday);
                  const status = getAttendanceStatus(member.id, dateStr, attendanceRecords);
                  return (
                    <div key={dateStr} className="attendance-item">
                      <span className="date-label">{formatSundayColumn(sunday)}</span>
                      <AttendanceButton
                        memberId={member.id}
                        date={dateStr}
                        status={status}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="desktop-view">
        <div className="table-wrapper">
          <table className="member-table">
            <thead>
              <tr>
                <th>
                  <button onClick={() => handleSort('lastName')}>
                    Name {sortField === 'lastName' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </button>
                </th>
                <th>Phone</th>
                <th>Address</th>
                <th>Born Again</th>
                <th>Group</th>
                {sundays.map((sunday) => (
                  <th key={formatDateForStorage(sunday)} className="attendance-header">
                    {formatSundayColumn(sunday)}
                  </th>
                ))}
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedMembers.map((member) => (
                <tr key={member.id} className={member.isCritical ? 'critical-row' : ''}>
                  <td className="name-cell">
                    <div className="name-container">
                      <span className="name">{member.firstName} {member.lastName}</span>
                      {member.isCritical && (
                        <AlertTriangle size={16} className="critical-icon" />
                      )}
                    </div>
                  </td>
                  <td>{formatPhoneNumber(member.phoneNumber)}</td>
                  <td className="address-cell">{member.buildingAddress}</td>
                  <td>
                    <span className={`status ${member.bornAgainStatus ? 'yes' : 'no'}`}>
                      {member.bornAgainStatus ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td>{member.congregationGroup}</td>
                  {sundays.map((sunday) => {
                    const dateStr = formatDateForStorage(sunday);
                    const status = getAttendanceStatus(member.id, dateStr, attendanceRecords);
                    return (
                      <td key={dateStr} className="attendance-cell">
                        <AttendanceButton
                          memberId={member.id}
                          date={dateStr}
                          status={status}
                        />
                      </td>
                    );
                  })}
                  <td className="actions-cell">
                    <div className="action-buttons">
                      <button
                        className="btn-sm btn-secondary"
                        onClick={() => onEditMember(member)}
                        title="Edit member"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        className="btn-sm btn-danger"
                        onClick={() => onDeleteMember(member.id)}
                        title="Delete member"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default MemberTable;
