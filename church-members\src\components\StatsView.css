/* Statistics View Styles */

.stats-view-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  padding: var(--spacing-lg);
}

.stats-section {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.section-title {
  margin: 0 0 var(--spacing-xl) 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.stat-card {
  background: var(--background-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card.primary {
  border-left-color: var(--primary-color);
}

.stat-card.success {
  border-left-color: var(--success-color);
}

.stat-card.warning {
  border-left-color: var(--warning-color);
}

.stat-card.danger {
  border-left-color: var(--danger-color);
}

.stat-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.stat-card.primary .stat-icon {
  background: var(--gradient-primary);
  color: white;
}

.stat-card.success .stat-icon {
  background: var(--gradient-success);
  color: white;
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.stat-card.danger .stat-icon {
  background: var(--gradient-danger);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.stat-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  transition: width 0.6s ease-out;
  border-radius: 4px;
}

.stat-card.success .progress-fill {
  background: var(--gradient-success);
}

.stat-card.danger .progress-fill {
  background: var(--gradient-danger);
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 40px;
}

/* Congregation Statistics */
.congregation-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.congregation-stat-card {
  background: var(--background-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  border: 2px solid var(--border-color);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.congregation-stat-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.congregation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.congregation-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.congregation-count {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
}

.congregation-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.metric {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-value.critical {
  color: var(--danger-color);
}

.congregation-progress {
  margin-top: var(--spacing-md);
}

/* Critical Members Statistics */
.critical-members-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.critical-member-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.critical-member-stat:hover {
  border-color: var(--danger-color);
  background: var(--danger-light);
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 1.125rem;
}

.member-contact {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.absence-stat {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--danger-color);
  font-weight: 600;
  font-size: 0.875rem;
  background: var(--danger-light);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
}

.more-critical-stat {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  font-style: italic;
  background: var(--border-light);
  border-radius: var(--radius-md);
}

/* Empty State */
.stats-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--border-color);
}

.empty-content {
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.empty-content h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.empty-content p {
  margin: 0;
  font-size: 1rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .stats-view-container {
    padding: var(--spacing-md);
    gap: var(--spacing-xl);
  }
  
  .stats-section {
    padding: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .congregation-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .critical-member-stat {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .stats-view-container {
    padding: var(--spacing-sm);
  }
  
  .stats-section {
    padding: var(--spacing-md);
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .congregation-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}
