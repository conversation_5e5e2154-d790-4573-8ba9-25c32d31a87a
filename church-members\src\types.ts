// Type definitions for the church membership application

export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string; // This serves as 'contact'
  buildingAddress: string; // This serves as 'residence'
  bornAgainStatus: boolean;
  congregationGroup: string;
  createdDate: string;
  lastUpdated: string;
  // New fields for New Believers tab
  dateOfBirth?: string;
  studies?: string;
  campus?: string;
  occupation?: string;
  yearOfStudy?: string;
  firstTime?: boolean; // First time giving life to Christ
  ministry?: string; // Dancing Stars, Choir, Ushers, Media, Airport stars, Arrival Stars
}

export interface AttendanceRecord {
  id: string;
  memberId: string;
  date: string; // YYYY-MM-DD format
  status: 'present' | 'absent';
  createdDate: string;
}

export interface Congregation {
  id: string;
  name: string;
  description?: string;
  createdDate: string;
}

export interface MemberWithAttendance extends Member {
  attendanceRecords: AttendanceRecord[];
  consecutiveAbsences: number;
  isCritical: boolean;
}

export interface AttendanceStats {
  totalMembers: number;
  presentCount: number;
  absentCount: number;
  attendancePercentage: number;
}

export interface DashboardStats {
  totalActiveMembers: number;
  currentMonthAttendancePercentage: number;
  criticalMembersCount: number;
  congregationStats: Record<string, AttendanceStats>;
}

export type TabType = 'all' | 'critical' | string; // string for congregation names
