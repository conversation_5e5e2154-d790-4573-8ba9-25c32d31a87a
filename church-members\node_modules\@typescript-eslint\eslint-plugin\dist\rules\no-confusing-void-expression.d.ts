import type { TSESLint } from '@typescript-eslint/utils';
export type Options = [
    {
        ignoreArrowShorthand?: boolean;
        ignoreVoidOperator?: boolean;
        ignoreVoidReturningFunctions?: boolean;
    }
];
export type MessageId = 'invalidVoidExpr' | 'invalidVoidExprArrow' | 'invalidVoidExprArrowWrapVoid' | 'invalidVoidExprReturn' | 'invalidVoidExprReturnLast' | 'invalidVoidExprReturnWrapVoid' | 'invalidVoidExprWrapVoid' | 'voidExprWrapVoid';
declare const _default: TSESLint.RuleModule<MessageId, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-confusing-void-expression.d.ts.map