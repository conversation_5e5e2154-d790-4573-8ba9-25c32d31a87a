/* Tab Navigation Styles - Mobile First */

.tab-navigation {
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.tab-list {
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  gap: var(--spacing-xs);
  padding: 0 var(--spacing-xs);
}

.tab-list::-webkit-scrollbar {
  display: none;
}

.tab-button {
  flex-shrink: 0;
  min-height: var(--touch-target);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-bottom: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: var(--surface-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--surface-color);
}

.tab-button.alert {
  color: var(--danger-color);
}

.tab-button.alert.active {
  border-bottom-color: var(--danger-color);
}

.tab-label {
  display: flex;
  align-items: center;
}

.tab-badge {
  display: flex;
  align-items: center;
  gap: 2px;
  background-color: var(--secondary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  justify-content: center;
}

.tab-badge.alert {
  background-color: var(--danger-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .tab-list {
    overflow-x: visible;
    justify-content: flex-start;
    padding: 0;
  }
  
  .tab-button {
    font-size: 1rem;
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .tab-button {
    min-width: 120px;
    justify-content: center;
  }
}
