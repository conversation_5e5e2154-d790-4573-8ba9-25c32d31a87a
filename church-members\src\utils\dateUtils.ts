// Date utility functions for the church membership application

import { format, startOfMonth, endOfMonth, eachDayOfInterval, parseISO } from 'date-fns';

/**
 * Get all Sundays in the current month
 */
export function getCurrentMonthSundays(): Date[] {
  const now = new Date();
  const start = startOfMonth(now);
  const end = endOfMonth(now);

  const allDays = eachDayOfInterval({ start, end });
  return allDays.filter(day => day.getDay() === 0); // Filter for Sundays
}

/**
 * Format date for display in attendance columns
 * @param date - Date to format
 * @returns Formatted string like "Sun 12/3"
 */
export function formatSundayColumn(date: Date): string {
  return format(date, 'EEE M/d');
}

/**
 * Format date for database storage (YYYY-MM-DD)
 */
export function formatDateForStorage(date: Date): string {
  return format(date, 'yyyy-MM-dd');
}

/**
 * Parse date from storage format
 */
export function parseDateFromStorage(dateString: string): Date {
  return parseISO(dateString);
}

/**
 * Get the last N Sundays from today
 */
export function getLastNSundays(n: number): Date[] {
  const sundays: Date[] = [];
  const today = new Date();
  let currentDate = new Date(today);
  
  // Go back to find the most recent Sunday (or today if it's Sunday)
  while (currentDate.getDay() !== 0) {
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  // Collect the last N Sundays
  for (let i = 0; i < n; i++) {
    sundays.unshift(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() - 7);
  }
  
  return sundays;
}

/**
 * Check if a date is a Sunday
 */
export function isSunday(date: Date): boolean {
  return date.getDay() === 0;
}

/**
 * Get the next Sunday from a given date
 */
export function getNextSunday(date: Date): Date {
  const nextSunday = new Date(date);
  const daysUntilSunday = 7 - date.getDay();
  nextSunday.setDate(date.getDate() + (daysUntilSunday === 7 ? 0 : daysUntilSunday));
  return nextSunday;
}

/**
 * Get the previous Sunday from a given date
 */
export function getPreviousSunday(date: Date): Date {
  const prevSunday = new Date(date);
  const daysSinceSunday = date.getDay();
  prevSunday.setDate(date.getDate() - (daysSinceSunday === 0 ? 7 : daysSinceSunday));
  return prevSunday;
}

/**
 * Check if two dates are the same day
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return formatDateForStorage(date1) === formatDateForStorage(date2);
}

/**
 * Format phone number for display
 */
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX if 10 digits
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  // Return original if not 10 digits
  return phone;
}
