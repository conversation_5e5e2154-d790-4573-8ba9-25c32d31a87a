import React, { useState } from 'react';
import type { MemberWithAttendance, AttendanceRecord } from '../types';
import { formatSundayColumn, formatDateForStorage, formatPhoneNumber } from '../utils/dateUtils';
import { getAttendanceStatus } from '../utils/attendanceUtils';
import { Edit, Trash2, AlertTriangle, Check, X, ChevronUp, ChevronDown } from 'lucide-react';
import './ExcelTableView.css';

interface ExcelTableViewProps {
  members: MemberWithAttendance[];
  attendanceRecords: AttendanceRecord[];
  sundays: Date[];
  onEditMember: (member: MemberWithAttendance) => void;
  onDeleteMember: (memberId: string) => void;
  onAttendanceToggle: (memberId: string, date: string, currentStatus: 'present' | 'absent' | null) => void;
}

type SortField = 'firstName' | 'lastName' | 'phoneNumber' | 'bornAgainStatus' | 'consecutiveAbsences';
type SortDirection = 'asc' | 'desc';

const ExcelTableView: React.FC<ExcelTableViewProps> = ({
  members,
  attendanceRecords,
  sundays,
  onEditMember,
  onDeleteMember,
  onAttendanceToggle,
}) => {
  const [sortField, setSortField] = useState<SortField>('lastName');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Sort members
  const sortedMembers = [...members].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const SortIcon: React.FC<{ field: SortField }> = ({ field }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? 
      <ChevronUp size={16} className="sort-icon" /> : 
      <ChevronDown size={16} className="sort-icon" />;
  };

  const AttendanceCell: React.FC<{
    memberId: string;
    date: string;
    status: 'present' | 'absent' | null;
  }> = ({ memberId, date, status }) => (
    <div className="attendance-cell">
      <button
        className={`excel-attendance-btn ${status || 'unmarked'}`}
        onClick={() => onAttendanceToggle(memberId, date, status)}
        title={status === 'present' ? 'Present' : status === 'absent' ? 'Absent' : 'Not marked'}
      >
        {status === 'present' ? <Check size={14} /> : 
         status === 'absent' ? <X size={14} /> : 
         <div className="unmarked-dot" />}
      </button>
    </div>
  );

  if (members.length === 0) {
    return (
      <div className="excel-empty-state">
        <div className="empty-content">
          <h3>No Members Yet</h3>
          <p>Start building your church community by adding your first member!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="excel-table-container">
      <div className="excel-table-wrapper">
        <table className="excel-table">
          <thead className="excel-header">
            <tr>
              <th className="excel-th row-number">#</th>
              <th className="excel-th sortable" onClick={() => handleSort('firstName')}>
                <div className="header-content">
                  <span>First Name</span>
                  <SortIcon field="firstName" />
                </div>
              </th>
              <th className="excel-th sortable" onClick={() => handleSort('lastName')}>
                <div className="header-content">
                  <span>Last Name</span>
                  <SortIcon field="lastName" />
                </div>
              </th>
              <th className="excel-th sortable" onClick={() => handleSort('phoneNumber')}>
                <div className="header-content">
                  <span>Phone</span>
                  <SortIcon field="phoneNumber" />
                </div>
              </th>
              <th className="excel-th address-col">Address</th>
              <th className="excel-th sortable" onClick={() => handleSort('bornAgainStatus')}>
                <div className="header-content">
                  <span>Born Again</span>
                  <SortIcon field="bornAgainStatus" />
                </div>
              </th>
              <th className="excel-th group-col">Group</th>
              {sundays.map((sunday) => (
                <th key={formatDateForStorage(sunday)} className="excel-th attendance-col">
                  <div className="attendance-header">
                    <span className="date-text">{formatSundayColumn(sunday)}</span>
                  </div>
                </th>
              ))}
              <th className="excel-th sortable" onClick={() => handleSort('consecutiveAbsences')}>
                <div className="header-content">
                  <span>Alerts</span>
                  <SortIcon field="consecutiveAbsences" />
                </div>
              </th>
              <th className="excel-th actions-col">Actions</th>
            </tr>
          </thead>
          <tbody className="excel-body">
            {sortedMembers.map((member, index) => (
              <tr key={member.id} className={`excel-row ${member.isCritical ? 'critical-row' : ''}`}>
                <td className="excel-cell row-number">{index + 1}</td>
                <td className="excel-cell name-cell">{member.firstName}</td>
                <td className="excel-cell name-cell">{member.lastName}</td>
                <td className="excel-cell phone-cell">{formatPhoneNumber(member.phoneNumber)}</td>
                <td className="excel-cell address-cell" title={member.buildingAddress}>
                  {member.buildingAddress}
                </td>
                <td className="excel-cell status-cell">
                  <span className={`status-indicator ${member.bornAgainStatus ? 'yes' : 'no'}`}>
                    {member.bornAgainStatus ? 'Yes' : 'No'}
                  </span>
                </td>
                <td className="excel-cell group-cell">{member.congregationGroup}</td>
                {sundays.map((sunday) => {
                  const dateStr = formatDateForStorage(sunday);
                  const status = getAttendanceStatus(member.id, dateStr, attendanceRecords);
                  return (
                    <td key={dateStr} className="excel-cell attendance-cell">
                      <AttendanceCell
                        memberId={member.id}
                        date={dateStr}
                        status={status}
                      />
                    </td>
                  );
                })}
                <td className="excel-cell alerts-cell">
                  {member.isCritical && (
                    <div className="alert-indicator">
                      <AlertTriangle size={16} />
                      <span>{member.consecutiveAbsences}</span>
                    </div>
                  )}
                </td>
                <td className="excel-cell actions-cell">
                  <div className="action-buttons">
                    <button
                      className="excel-action-btn edit"
                      onClick={() => onEditMember(member)}
                      title="Edit member"
                    >
                      <Edit size={14} />
                    </button>
                    <button
                      className="excel-action-btn delete"
                      onClick={() => onDeleteMember(member.id)}
                      title="Delete member"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExcelTableView;
