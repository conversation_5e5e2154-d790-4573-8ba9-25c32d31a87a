// Attendance calculation utilities

import type { Member, AttendanceRecord, MemberWithAttendance, AttendanceStats } from '../types';
import { getLastNSundays, formatDateForStorage } from './dateUtils';

/**
 * Calculate consecutive absences for a member
 */
export function calculateConsecutiveAbsences(
  memberId: string,
  attendanceRecords: AttendanceRecord[]
): number {
  const memberRecords = attendanceRecords
    .filter(record => record.memberId === memberId)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); // Most recent first

  let consecutiveAbsences = 0;
  const recentSundays = getLastNSundays(8); // Check last 8 Sundays

  for (const sunday of recentSundays) {
    const sundayStr = formatDateForStorage(sunday);
    const record = memberRecords.find(r => r.date === sundayStr);

    if (record && record.status === 'absent') {
      consecutiveAbsences++;
    } else if (record && record.status === 'present') {
      break; // Stop counting if they were present
    }
    // If no record exists, we skip this Sunday but continue checking older ones
  }

  return consecutiveAbsences;
}

/**
 * Determine if a member is critical (2+ consecutive absences)
 */
export function isMemberCritical(consecutiveAbsences: number): boolean {
  return consecutiveAbsences >= 2;
}

/**
 * Enhance members with attendance data
 */
export function enhanceMembersWithAttendance(
  members: Member[],
  attendanceRecords: AttendanceRecord[]
): MemberWithAttendance[] {
  return members.map(member => {
    const memberAttendance = attendanceRecords.filter(
      record => record.memberId === member.id
    );
    
    const consecutiveAbsences = calculateConsecutiveAbsences(member.id, attendanceRecords);
    const isCritical = isMemberCritical(consecutiveAbsences);

    return {
      ...member,
      attendanceRecords: memberAttendance,
      consecutiveAbsences,
      isCritical,
    };
  });
}

/**
 * Calculate attendance statistics for a group of members
 */
export function calculateAttendanceStats(
  members: Member[],
  attendanceRecords: AttendanceRecord[],
  date: string
): AttendanceStats {
  const totalMembers = members.length;
  const dateRecords = attendanceRecords.filter(record => record.date === date);
  
  let presentCount = 0;
  let absentCount = 0;

  members.forEach(member => {
    const memberRecord = dateRecords.find(record => record.memberId === member.id);
    if (memberRecord) {
      if (memberRecord.status === 'present') {
        presentCount++;
      } else {
        absentCount++;
      }
    } else {
      // No record means absent
      absentCount++;
    }
  });

  const attendancePercentage = totalMembers > 0 ? (presentCount / totalMembers) * 100 : 0;

  return {
    totalMembers,
    presentCount,
    absentCount,
    attendancePercentage: Math.round(attendancePercentage * 100) / 100, // Round to 2 decimal places
  };
}

/**
 * Get current month attendance percentage for members
 */
export function getCurrentMonthAttendancePercentage(
  members: Member[],
  attendanceRecords: AttendanceRecord[]
): number {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  
  // Filter attendance records for current month
  const currentMonthRecords = attendanceRecords.filter(record => {
    const recordDate = new Date(record.date);
    return recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear;
  });

  if (currentMonthRecords.length === 0) return 0;

  // Get unique dates in current month
  const uniqueDates = [...new Set(currentMonthRecords.map(record => record.date))];
  
  let totalPossibleAttendance = members.length * uniqueDates.length;
  let totalPresent = currentMonthRecords.filter(record => record.status === 'present').length;

  return totalPossibleAttendance > 0 ? (totalPresent / totalPossibleAttendance) * 100 : 0;
}

/**
 * Filter critical members
 */
export function getCriticalMembers(membersWithAttendance: MemberWithAttendance[]): MemberWithAttendance[] {
  return membersWithAttendance.filter(member => member.isCritical);
}

/**
 * Get attendance status for a specific member and date
 */
export function getAttendanceStatus(
  memberId: string,
  date: string,
  attendanceRecords: AttendanceRecord[]
): 'present' | 'absent' | null {
  const record = attendanceRecords.find(
    r => r.memberId === memberId && r.date === date
  );
  return record ? record.status : null;
}
