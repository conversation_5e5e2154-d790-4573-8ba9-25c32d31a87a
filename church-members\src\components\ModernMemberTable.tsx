import React, { useState } from 'react';
import type { MemberWithAttendance, AttendanceRecord } from '../types';
import { formatSundayColumn, formatDateForStorage, formatPhoneNumber } from '../utils/dateUtils';
import { getAttendanceStatus } from '../utils/attendanceUtils';
import { Phone, MapPin, AlertTriangle, Check, X, Search, Filter, Users } from 'lucide-react';
import './ModernMemberTable.css';

interface ModernMemberTableProps {
  members: MemberWithAttendance[];
  attendanceRecords: AttendanceRecord[];
  sundays: Date[];
  activeTab: string;
  onEditMember: (member: MemberWithAttendance) => void;
  onDeleteMember: (memberId: string) => void;
  onAttendanceToggle: (memberId: string, date: string, currentStatus: 'present' | 'absent' | null) => void;
}

const ModernMemberTable: React.FC<ModernMemberTableProps> = ({
  members,
  attendanceRecords,
  sundays,
  activeTab,
  onEditMember,
  onDeleteMember,
  onAttendanceToggle,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Filter members based on search term
  const filteredMembers = members.filter(member =>
    `${member.firstName} ${member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.phoneNumber.includes(searchTerm) ||
    member.buildingAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const AttendanceButton: React.FC<{
    memberId: string;
    date: string;
    status: 'present' | 'absent' | null;
  }> = ({ memberId, date, status }) => (
    <button
      className={`modern-attendance-btn ${status || 'unmarked'}`}
      onClick={() => onAttendanceToggle(memberId, date, status)}
      title={status === 'present' ? 'Present - Click to mark absent' : 
             status === 'absent' ? 'Absent - Click to mark present' : 
             'Not marked - Click to mark present'}
    >
      {status === 'present' ? <Check size={16} /> : 
       status === 'absent' ? <X size={16} /> : 
       <div className="unmarked-indicator" />}
    </button>
  );

  const getTabMessage = () => {
    switch (activeTab) {
      case 'overall':
        return 'All members across your church';
      case 'critical':
        return 'Members who need your attention';
      case 'new-believers':
        return 'New believers with detailed information';
      default:
        return 'Members in this group';
    }
  };

  if (members.length === 0) {
    return (
      <div className="modern-empty-state">
        <div className="empty-icon">
          <Users size={64} />
        </div>
        <h3>No members found</h3>
        <p>{getTabMessage()}</p>
        <p>Add your first member to get started!</p>
      </div>
    );
  }

  return (
    <div className="modern-member-table">
      {/* Search and Filter Bar */}
      <div className="table-controls">
        <div className="search-wrapper">
          <Search size={20} className="search-icon" />
          <input
            type="text"
            placeholder="Search members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="modern-search-input"
          />
        </div>
        <button
          className={`filter-btn ${showFilters ? 'active' : ''}`}
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} />
        </button>
      </div>

      {/* Member Cards */}
      <div className="member-cards-grid">
        {filteredMembers.map((member) => (
          <div key={member.id} className={`modern-member-card ${member.isCritical ? 'critical' : ''}`}>
            {/* Member Header */}
            <div className="member-card-header">
              <div className="member-info">
                <h3 className="member-name">
                  {member.firstName} {member.lastName}
                </h3>
                {member.isCritical && (
                  <div className="critical-badge">
                    <AlertTriangle size={14} />
                    <span>Needs Contact</span>
                  </div>
                )}
              </div>
              <div className="member-actions">
                <button
                  className="action-btn edit"
                  onClick={() => onEditMember(member)}
                  title="Edit member"
                >
                  EDIT
                </button>
                <button
                  className="action-btn delete"
                  onClick={() => onDeleteMember(member.id)}
                  title="Delete member"
                >
                  DEL
                </button>
              </div>
            </div>

            {/* Member Details */}
            <div className="member-details">
              <div className="detail-row">
                <Phone size={16} className="detail-icon" />
                <span>{formatPhoneNumber(member.phoneNumber)}</span>
              </div>
              <div className="detail-row">
                <MapPin size={16} className="detail-icon" />
                <span>{member.buildingAddress}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Born Again:</span>
                <span className={`status-badge ${member.bornAgainStatus ? 'yes' : 'no'}`}>
                  {member.bornAgainStatus ? 'Yes' : 'No'}
                </span>
              </div>

              {/* New Believers Additional Fields */}
              {activeTab === 'new-believers' && (
                <>
                  {member.dateOfBirth && (
                    <div className="detail-row">
                      <span className="detail-label">Date of Birth:</span>
                      <span>{new Date(member.dateOfBirth).toLocaleDateString()}</span>
                    </div>
                  )}
                  {member.studies && (
                    <div className="detail-row">
                      <span className="detail-label">Studies:</span>
                      <span>{member.studies}</span>
                    </div>
                  )}
                  {member.campus && (
                    <div className="detail-row">
                      <span className="detail-label">Campus:</span>
                      <span>{member.campus}</span>
                    </div>
                  )}
                  {member.occupation && (
                    <div className="detail-row">
                      <span className="detail-label">Occupation:</span>
                      <span>{member.occupation}</span>
                    </div>
                  )}
                  {member.yearOfStudy && (
                    <div className="detail-row">
                      <span className="detail-label">Year of Study:</span>
                      <span>{member.yearOfStudy}</span>
                    </div>
                  )}
                  {member.ministry && (
                    <div className="detail-row">
                      <span className="detail-label">Ministry:</span>
                      <span>{member.ministry}</span>
                    </div>
                  )}
                  {member.firstTime && (
                    <div className="detail-row">
                      <span className="detail-label">First Time:</span>
                      <span className="status-badge yes">Yes</span>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Attendance Section */}
            <div className="attendance-section">
              <h4 className="attendance-title">Recent Attendance</h4>
              <div className="attendance-grid">
                {sundays.map((sunday) => {
                  const dateStr = formatDateForStorage(sunday);
                  const status = getAttendanceStatus(member.id, dateStr, attendanceRecords);
                  return (
                    <div key={dateStr} className="attendance-item">
                      <span className="attendance-date">{formatSundayColumn(sunday)}</span>
                      <AttendanceButton
                        memberId={member.id}
                        date={dateStr}
                        status={status}
                      />
                    </div>
                  );
                })}
              </div>
              {member.consecutiveAbsences > 0 && (
                <div className="absence-warning">
                  <AlertTriangle size={14} />
                  <span>{member.consecutiveAbsences} consecutive absence{member.consecutiveAbsences > 1 ? 's' : ''}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredMembers.length === 0 && searchTerm && (
        <div className="no-results">
          <Search size={48} />
          <h3>No results found</h3>
          <p>Try adjusting your search terms</p>
        </div>
      )}
    </div>
  );
};

export default ModernMemberTable;
