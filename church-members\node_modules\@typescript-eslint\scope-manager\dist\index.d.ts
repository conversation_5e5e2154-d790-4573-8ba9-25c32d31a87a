export { analyze, type AnalyzeOptions } from './analyze';
export * from './definition';
export { PatternVisitor, type PatternVisitorCallback, type PatternVisitorOptions, } from './referencer/PatternVisitor';
export { Reference } from './referencer/Reference';
export { Visitor } from './referencer/Visitor';
export * from './scope';
export { ScopeManager } from './ScopeManager';
export * from './variable';
//# sourceMappingURL=index.d.ts.map